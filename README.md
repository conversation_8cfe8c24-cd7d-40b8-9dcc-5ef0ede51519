# Oh My AI2API - Z.AI Proxy

基于Golang实现的Z.AI代理服务，提供OpenAI兼容的API接口。

## 功能特性

- 🚀 OpenAI兼容的API接口
- 🔄 支持流式和非流式响应
- 🍪 智能Cookie管理和轮询
- 🔐 灵活的认证机制（本地API Key或透传上游认证）
- 🌐 代理支持（HTTP/HTTPS）
- 🎯 内容转换（thinking标签处理）
- 📊 健康检查和自动恢复
- 🔧 可扩展架构，便于添加更多AI服务

## 快速开始

### 1. 配置环境变量

复制示例配置文件：
```bash
cp .env.example .env
```

编辑`.env`文件，设置必要的配置：
```bash
# 必需：设置Z.AI的cookies
Z_AI_COOKIES=your_z_ai_cookie_1,your_z_ai_cookie_2

# 可选：其他配置
API_KEY=sk-your-custom-key
PORT=8000
LOG_LEVEL=INFO
```

### 2. 编译运行

```bash
# 下载依赖
go mod tidy

# 编译
go build -o oh-my-ai2api.exe .

# 运行
./oh-my-ai2api.exe
```

### 3. 使用API

服务启动后，Z.AI相关的端点将在`/zai`路径下可用：

#### 获取模型列表
```bash
curl http://localhost:8000/zai/v1/models \
  -H "Authorization: Bearer sk-z2api-key-2024"
```

#### 聊天完成（非流式）
```bash
curl http://localhost:8000/zai/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-z2api-key-2024" \
  -d '{
    "model": "GLM-4.5",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ],
    "stream": false
  }'
```

#### 聊天完成（流式）
```bash
curl http://localhost:8000/zai/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-z2api-key-2024" \
  -d '{
    "model": "GLM-4.5",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ],
    "stream": true
  }'
```

## API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 服务信息 |
| `/health` | GET | 健康检查 |
| `/zai/v1/models` | GET | 获取Z.AI模型列表 |
| `/zai/v1/chat/completions` | POST | Z.AI聊天完成 |

## 配置说明

| 环境变量 | 默认值 | 描述 |
|----------|--------|------|
| `HOST` | `0.0.0.0` | 服务器监听地址 |
| `PORT` | `8000` | 服务器端口 |
| `API_KEY` | `sk-z2api-key-2024` | 本地API密钥 |
| `Z_AI_COOKIES` | - | Z.AI认证cookies（必需） |
| `SHOW_THINK_TAGS` | `false` | 是否显示思考标签 |
| `DEFAULT_STREAM` | `false` | 默认是否使用流式响应 |
| `LOG_LEVEL` | `INFO` | 日志级别 |
| `STREAM_TIMEOUT` | `30` | 流式响应超时时间（秒） |

## 认证机制

1. **本地认证**：当请求的Authorization header匹配配置的`API_KEY`时，使用本地cookie池进行Z.AI认证
2. **透传认证**：当Authorization header不匹配本地API_KEY时，直接透传给Z.AI进行认证

## 架构设计

项目采用分层架构，便于扩展：

```
oh-my-ai2api/
├── main.go                 # 主入口
├── internal/
│   ├── config/            # 配置管理
│   ├── common/            # 通用工具
│   ├── zai/               # Z.AI相关逻辑
│   └── server/            # HTTP服务器
```

## 开发说明

- 使用Gin框架提供HTTP服务
- 支持CORS跨域请求
- 全局错误处理，不在controller中使用try-catch
- 使用普通注释，不使用Swagger注解
- 遵循Go语言最佳实践

## 许可证

MIT License
