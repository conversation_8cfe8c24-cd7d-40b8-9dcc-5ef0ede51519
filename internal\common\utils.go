package common

import (
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
)

// Logger 简单的日志工具
type Logger struct {
	Level string
}

// NewLogger 创建新的日志器
func NewLogger(level string) *Logger {
	return &Logger{Level: level}
}

// Debug 调试日志
func (l *Logger) Debug(msg string) {
	if l.Level == "DEBUG" {
		log.Printf("[DEBUG] %s - %s", time.Now().Format(time.RFC3339), msg)
	}
}

// Info 信息日志
func (l *Logger) Info(msg string) {
	if l.Level == "DEBUG" || l.Level == "INFO" {
		log.Printf("[INFO] %s - %s", time.Now().Format(time.RFC3339), msg)
	}
}

// Warning 警告日志
func (l *Logger) Warning(msg string) {
	log.Printf("[WARNING] %s - %s", time.Now().Format(time.RFC3339), msg)
}

// Error 错误日志
func (l *Logger) Error(msg string) {
	log.Printf("[ERROR] %s - %s", time.Now().Format(time.RFC3339), msg)
}

// GenerateUUID 生成UUID
func GenerateUUID() string {
	return uuid.New().String()
}

// GenerateCompletionID 生成聊天完成ID
func GenerateCompletionID() string {
	id := strings.ReplaceAll(GenerateUUID(), "-", "")
	if len(id) > 29 {
		id = id[:29]
	}
	return "chatcmpl-" + id
}

// TransformContent 转换内容，处理thinking标签
func TransformContent(content string, showThinkTags bool) string {
	if content == "" {
		return content
	}

	if !showThinkTags {
		// 移除<details>块（思考内容）
		detailsRegex := regexp.MustCompile(`<details[^>]*>.*?</details>`)
		content = detailsRegex.ReplaceAllString(content, "")
		
		// 移除未闭合的<details>块
		detailsOpenRegex := regexp.MustCompile(`<details[^>]*>.*?(?=\s*[A-Z]|\s*\d|\s*$)`)
		content = detailsOpenRegex.ReplaceAllString(content, "")
		
		content = strings.TrimSpace(content)
	} else {
		// 替换<details>为<think>
		content = regexp.MustCompile(`<details[^>]*>`).ReplaceAllString(content, "<think>")
		content = strings.ReplaceAll(content, "</details>", "</think>")
		
		// 移除<summary>标签
		summaryRegex := regexp.MustCompile(`<summary>.*?</summary>`)
		content = summaryRegex.ReplaceAllString(content, "")
		
		// 如果没有闭合的</think>，添加它
		if strings.Contains(content, "<think>") && !strings.Contains(content, "</think>") {
			thinkStart := strings.Index(content, "<think>")
			if thinkStart != -1 {
				answerRegex := regexp.MustCompile(`\n\s*[A-Z0-9]`)
				matches := answerRegex.FindStringIndex(content[thinkStart:])
				if matches != nil {
					insertPos := thinkStart + matches[0]
					content = content[:insertPos] + "</think>\n" + content[insertPos:]
				} else {
					content += "</think>"
				}
			}
		}
	}

	return strings.TrimSpace(content)
}
