package gmi

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// Handler GMI处理器
type Handler struct {
	proxyHandler *ProxyHandler
	logger       *common.Logger
}

// NewHandler 创建新的处理器
func NewHandler(gmiConfig *config.GMIConfig, proxyConfig *config.ProxyConfig, logger *common.Logger) *Handler {
	return &Handler{
		proxyHandler: NewProxyHandler(gmiConfig, proxyConfig, logger),
		logger:       logger,
	}
}

// HandleModels 处理模型列表请求
// GET /gmi/v1/models
func (h *Handler) HandleModels(c *gin.Context) {
	h.logger.Debug("=== GMI HandleModels Started ===")

	// 验证认证
	if !h.validateAuth(c) {
		return
	}

	// 获取模型列表
	models, err := h.proxyHandler.GetModels()
	if err != nil {
		h.logger.Error("Failed to get GMI models: " + err.Error())
		common.SendBadRequest(c, err.Error())
		return
	}

	c.JSON(http.StatusOK, models)
	h.logger.Debug("=== GMI HandleModels Completed ===")
}

// HandleChatCompletions 处理聊天完成请求
// POST /gmi/v1/chat/completions
func (h *Handler) HandleChatCompletions(c *gin.Context) {
	h.logger.Debug("=== GMI HandleChatCompletions Started ===")

	// 验证认证
	if !h.validateAuth(c) {
		return
	}

	// 解析请求体
	var request ChatCompletionRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("Failed to parse request body: " + err.Error())
		common.SendBadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	h.logger.Debug(fmt.Sprintf("Request model: %s, streaming: %v", request.Model, request.Stream != nil && *request.Stream))

	// 调用代理处理器
	result, err := h.proxyHandler.HandleChatCompletion(&request)
	if err != nil {
		h.logger.Error("ProxyHandler error: " + err.Error())
		h.handleProxyError(c, err)
		return
	}

	// 处理响应
	if chunks, ok := result.([]string); ok {
		// 流式响应
		h.logger.Debug(fmt.Sprintf("Sending streaming response with %d chunks", len(chunks)))
		h.sendStreamingResponse(c, chunks)
	} else if response, ok := result.(*ChatCompletionResponse); ok {
		// 非流式响应
		h.logger.Debug("Sending non-streaming response")
		c.JSON(http.StatusOK, response)
	} else {
		h.logger.Error("Unknown response type")
		common.SendInternalError(c, "Unknown response type")
		return
	}

	h.logger.Debug("=== GMI HandleChatCompletions Completed ===")
}

// validateAuth 验证认证
func (h *Handler) validateAuth(c *gin.Context) bool {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		h.logger.Warning("Missing Authorization header")
		common.SendUnauthorized(c, "Missing or invalid Authorization header")
		return false
	}

	if !strings.HasPrefix(authHeader, "Bearer ") {
		h.logger.Warning("Invalid Authorization header format")
		common.SendUnauthorized(c, "Missing or invalid Authorization header")
		return false
	}

	token := strings.TrimPrefix(authHeader, "Bearer ")
	if !h.proxyHandler.ValidateToken(token) {
		h.logger.Warning("Invalid token: " + token)
		common.SendUnauthorized(c, "Invalid token")
		return false
	}

	h.logger.Debug("Authentication successful")
	return true
}

// sendStreamingResponse 发送流式响应
func (h *Handler) sendStreamingResponse(c *gin.Context, chunks []string) {
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")

	c.Status(http.StatusOK)

	// 发送所有chunks
	for _, chunk := range chunks {
		c.Writer.WriteString(chunk)
		c.Writer.Flush()
	}
}

// handleProxyError 处理代理错误
func (h *Handler) handleProxyError(c *gin.Context, err error) {
	errorMsg := err.Error()
	
	switch {
	case contains(errorMsg, "authentication"):
		h.logger.Error("Error type: Authentication error")
		common.SendUnauthorized(c, errorMsg)
	case contains(errorMsg, "timeout"):
		h.logger.Error("Error type: Timeout error")
		common.SendServiceUnavailable(c, errorMsg)
	case contains(errorMsg, "API error"):
		h.logger.Error("Error type: API error")
		common.SendBadRequest(c, errorMsg)
	default:
		h.logger.Error("Error type: Internal server error")
		common.SendInternalError(c, errorMsg)
	}
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}
