package gmi

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"time"

	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// ProxyHandler GMI代理处理器
type ProxyHandler struct {
	client *Client
	logger *common.Logger
}

// NewProxyHandler 创建新的代理处理器
func NewProxyHandler(gmiConfig *config.GMIConfig, proxyConfig *config.ProxyConfig, logger *common.Logger) *ProxyHandler {
	client := NewClient(gmiConfig, proxyConfig, logger)
	
	return &ProxyHandler{
		client: client,
		logger: logger,
	}
}

// HandleChatCompletion 处理聊天完成请求
func (p *ProxyHandler) HandleChatCompletion(request *ChatCompletionRequest) (interface{}, error) {
	p.logger.Debug("=== GMI ProxyHandler.HandleChatCompletion Started ===")

	// 构建GMI请求
	gmiRequest := p.buildGMIRequest(request)
	
	// 发送请求到GMI
	resp, err := p.client.ChatCompletion(gmiRequest)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 确定是否流式响应
	isStreaming := request.Stream != nil && *request.Stream

	if isStreaming {
		return p.handleStreamingResponse(resp.Body, request.Model)
	} else {
		return p.handleNonStreamingResponse(resp.Body, request.Model)
	}
}

// buildGMIRequest 构建GMI请求
func (p *ProxyHandler) buildGMIRequest(request *ChatCompletionRequest) *GMIRequest {
	gmiReq := &GMIRequest{
		Messages: request.Messages,
		Model:    request.Model,
		Stream:   request.Stream != nil && *request.Stream,
	}

	// 设置默认值
	if request.Temperature != nil {
		gmiReq.Temperature = request.Temperature
	} else {
		temp := 0.5
		gmiReq.Temperature = &temp
	}

	if request.MaxTokens != nil {
		gmiReq.MaxTokens = request.MaxTokens
	} else {
		maxTokens := 4096
		gmiReq.MaxTokens = &maxTokens
	}

	if request.TopP != nil {
		gmiReq.TopP = request.TopP
	} else {
		topP := 0.95
		gmiReq.TopP = &topP
	}

	// 如果没有指定模型，使用默认模型
	if gmiReq.Model == "" {
		gmiReq.Model = "Qwen3-Coder-480B-A35B-Instruct-FP8"
	}

	return gmiReq
}

// handleStreamingResponse 处理流式响应
func (p *ProxyHandler) handleStreamingResponse(body io.Reader, model string) ([]string, error) {
	p.logger.Debug("=== handleStreamingResponse Started ===")

	var chunks []string

	// 直接返回GMI的流式数据
	// 注意：这里简化处理，实际可能需要根据GMI的具体流式格式进行转换
	scanner := bufio.NewScanner(body)
	for scanner.Scan() {
		line := scanner.Text()
		chunks = append(chunks, line+"\n")
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	p.logger.Debug(fmt.Sprintf("Collected %d streaming chunks", len(chunks)))
	return chunks, nil
}

// handleNonStreamingResponse 处理非流式响应
func (p *ProxyHandler) handleNonStreamingResponse(body io.Reader, model string) (*ChatCompletionResponse, error) {
	p.logger.Debug("=== handleNonStreamingResponse Started ===")

	// 读取响应体
	responseData, err := io.ReadAll(body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 解析GMI响应
	var gmiResp GMIResponse
	if err := json.Unmarshal(responseData, &gmiResp); err != nil {
		return nil, fmt.Errorf("failed to parse GMI response: %w", err)
	}

	// 转换为OpenAI格式
	return p.convertToOpenAIResponse(&gmiResp, model), nil
}

// convertToOpenAIResponse 转换为OpenAI格式响应
func (p *ProxyHandler) convertToOpenAIResponse(gmiResp *GMIResponse, model string) *ChatCompletionResponse {
	response := &ChatCompletionResponse{
		ID:      p.generateCompletionID(),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   model,
		Usage:   gmiResp.Usage,
	}

	// 处理响应内容
	var content string
	var finishReason string

	if len(gmiResp.Choices) > 0 {
		// 使用choices中的内容
		choice := gmiResp.Choices[0]
		content = choice.Message.Content
		finishReason = choice.FinishReason
	} else if gmiResp.Result != "" {
		// 使用result字段
		content = gmiResp.Result
		finishReason = "stop"
	}

	response.Choices = []Choice{{
		Index: 0,
		Message: Message{
			Role:    "assistant",
			Content: content,
		},
		FinishReason: finishReason,
	}}

	// 如果没有usage信息，设置默认值
	if response.Usage.PromptTokens == 0 && response.Usage.CompletionTokens == 0 {
		response.Usage = Usage{
			PromptTokens:     0,
			CompletionTokens: 0,
			TotalTokens:      0,
		}
	}

	return response
}

// generateCompletionID 生成完成ID
func (p *ProxyHandler) generateCompletionID() string {
	// 生成随机ID
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, 10)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return "chatcmpl-" + string(b)
}

// GetModels 获取模型列表
func (p *ProxyHandler) GetModels() (*ModelsResponse, error) {
	return p.client.GetModels()
}

// ValidateToken 验证token
func (p *ProxyHandler) ValidateToken(token string) bool {
	return p.client.ValidateToken(token)
}
