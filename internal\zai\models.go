package zai

// ChatCompletionRequest OpenAI聊天完成请求
type ChatCompletionRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
	Stream   *bool     `json:"stream,omitempty"`
}

// Message 消息
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatCompletionResponse OpenAI聊天完成响应
type ChatCompletionResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
}

// Choice 选择
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message,omitempty"`
	Delta        *Delta  `json:"delta,omitempty"`
	FinishReason *string `json:"finish_reason"`
}

// Delta 增量内容
type Delta struct {
	Content string `json:"content,omitempty"`
}

// ChatCompletionChunk 流式响应块
type ChatCompletionChunk struct {
	ID      string        `json:"id"`
	Object  string        `json:"object"`
	Created int64         `json:"created"`
	Model   string        `json:"model"`
	Choices []ChunkChoice `json:"choices"`
}

// ChunkChoice 流式选择
type ChunkChoice struct {
	Index        int     `json:"index"`
	Delta        Delta   `json:"delta"`
	FinishReason *string `json:"finish_reason"`
}

// ModelsResponse 模型列表响应
type ModelsResponse struct {
	Object string  `json:"object"`
	Data   []Model `json:"data"`
}

// Model 模型信息
type Model struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	OwnedBy string `json:"owned_by"`
}

// ZAIRequest Z.AI请求格式
type ZAIRequest struct {
	Stream          bool                   `json:"stream"`
	Model           string                 `json:"model"`
	Messages        []Message              `json:"messages"`
	BackgroundTasks map[string]bool        `json:"background_tasks"`
	ChatID          string                 `json:"chat_id"`
	Features        map[string]bool        `json:"features"`
	ID              string                 `json:"id"`
	MCPServers      []string               `json:"mcp_servers"`
	ModelItem       ZAIModelItem           `json:"model_item"`
	Params          map[string]interface{} `json:"params"`
	ToolServers     []string               `json:"tool_servers"`
	Variables       map[string]string      `json:"variables"`
}

// ZAIModelItem Z.AI模型项
type ZAIModelItem struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	OwnedBy string `json:"owned_by"`
}

// ZAIResponse Z.AI响应格式
type ZAIResponse struct {
	Data ZAIData `json:"data"`
}

// ZAIData Z.AI数据
type ZAIData struct {
	ID           string `json:"id,omitempty"`
	DeltaContent string `json:"delta_content,omitempty"`
	Phase        string `json:"phase,omitempty"`
}

// AuthInfo 认证信息
type AuthInfo struct {
	UseLocalAuth bool
	UpstreamAuth string
}

// StreamChunk 流式数据块
type StreamChunk struct {
	Data string
	Done bool
}
