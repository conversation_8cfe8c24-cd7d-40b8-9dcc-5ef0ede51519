package zai

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// ProxyHandler 代理处理器
type ProxyHandler struct {
	settings      *config.Settings
	cookieManager *CookieManager
	logger        *common.Logger
}

// NewProxyHandler 创建新的代理处理器
func NewProxyHandler(settings *config.Settings, logger *common.Logger) *ProxyHandler {
	cookieManager := NewCookieManager(settings.Cookies, logger, settings.UpstreamURL, settings.UpstreamModel)
	
	return &ProxyHandler{
		settings:      settings,
		cookieManager: cookieManager,
		logger:        logger,
	}
}

// HandleChatCompletion 处理聊天完成请求
func (p *ProxyHandler) HandleChatCompletion(request *ChatCompletionRequest, auth *AuthInfo) (interface{}, error) {
	p.logger.Debug("=== ProxyHandler.HandleChatCompletion Started ===")
	
	// 如果使用本地认证，检查cookies是否配置
	if auth.UseLocalAuth && len(p.settings.Cookies) == 0 {
		p.logger.Error("Local auth requested but no cookies configured")
		return nil, errors.New("service unavailable: No Z.AI cookies configured. Please set Z_AI_COOKIES environment variable")
	}

	// 确定是否流式响应
	isStreaming := p.settings.DefaultStream
	if request.Stream != nil {
		isStreaming = *request.Stream
	}

	p.logger.Debug(fmt.Sprintf("Streaming mode: %v", isStreaming))

	if isStreaming {
		return p.handleStreamingResponse(request, auth)
	} else {
		return p.handleNonStreamingResponse(request, auth)
	}
}

// handleStreamingResponse 处理流式响应
func (p *ProxyHandler) handleStreamingResponse(request *ChatCompletionRequest, auth *AuthInfo) ([]string, error) {
	p.logger.Debug("=== handleStreamingResponse Started ===")

	// 获取认证信息
	headers, cookie, err := p.buildHeaders(auth)
	if err != nil {
		return nil, err
	}

	// 构建Z.AI请求
	requestData := p.buildZAIRequest(request)
	
	// 打印请求数据
	if requestJSON, err := json.Marshal(requestData); err == nil {
		p.logger.Info("=== ZAI Upstream Request ===")
		p.logger.Info("Request to upstream: " + string(requestJSON))
	}

	// 发送请求
	resp, err := p.makeRequest(headers, requestData)
	if err != nil {
		if auth.UseLocalAuth && cookie != "" {
			p.cookieManager.MarkCookieFailed(cookie)
		}
		return nil, err
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode == 401 {
		if auth.UseLocalAuth && cookie != "" {
			p.cookieManager.MarkCookieFailed(cookie)
		}
		return nil, errors.New("invalid authentication")
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("upstream error: HTTP %d", resp.StatusCode)
	}

	if auth.UseLocalAuth && cookie != "" {
		p.cookieManager.MarkCookieSuccess(cookie)
	}

	// 处理流式数据
	return p.processStreamingData(resp.Body, request.Model)
}

// handleNonStreamingResponse 处理非流式响应
func (p *ProxyHandler) handleNonStreamingResponse(request *ChatCompletionRequest, auth *AuthInfo) (*ChatCompletionResponse, error) {
	p.logger.Debug("=== handleNonStreamingResponse Started ===")

	// 获取认证信息
	headers, cookie, err := p.buildHeaders(auth)
	if err != nil {
		return nil, err
	}

	// 构建Z.AI请求
	requestData := p.buildZAIRequest(request)

	// 打印请求数据
	if requestJSON, err := json.Marshal(requestData); err == nil {
		p.logger.Info("=== ZAI Upstream Request ===")
		p.logger.Info("Request to upstream: " + string(requestJSON))
	}

	// 发送请求
	resp, err := p.makeRequest(headers, requestData)
	if err != nil {
		if auth.UseLocalAuth && cookie != "" {
			p.cookieManager.MarkCookieFailed(cookie)
		}
		return nil, err
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode == 401 {
		if auth.UseLocalAuth && cookie != "" {
			p.cookieManager.MarkCookieFailed(cookie)
		}
		return nil, errors.New("invalid authentication")
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("upstream error: HTTP %d", resp.StatusCode)
	}

	if auth.UseLocalAuth && cookie != "" {
		p.cookieManager.MarkCookieSuccess(cookie)
	}

	// 收集所有流式数据并聚合
	chunks, err := p.collectStreamingData(resp.Body)
	if err != nil {
		return nil, err
	}

	return p.processNonStreamingChunks(chunks, request.Model)
}

// buildHeaders 构建请求头
func (p *ProxyHandler) buildHeaders(auth *AuthInfo) (map[string]string, string, error) {
	headers := map[string]string{
		"Content-Type":         "application/json",
		"User-Agent":          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
		"Accept":              "application/json, text/event-stream",
		"Accept-Language":     "zh-CN",
		"sec-ch-ua":           `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`,
		"sec-ch-ua-mobile":    "?0",
		"sec-ch-ua-platform":  `"macOS"`,
		"x-fe-version":        "prod-fe-1.0.53",
		"Origin":              "https://chat.z.ai",
		"Referer":             "https://chat.z.ai/c/069723d5-060b-404f-992c-4705f1554c4c",
	}

	var cookie string
	if auth.UseLocalAuth {
		// 使用本地cookie认证
		cookie = p.cookieManager.GetNextCookie()
		if cookie == "" {
			return nil, "", errors.New("no available cookies")
		}
		headers["Authorization"] = "Bearer " + cookie
	} else {
		// 透传上游认证
		headers["Authorization"] = auth.UpstreamAuth
	}

	return headers, cookie, nil
}

// buildZAIRequest 构建Z.AI请求
func (p *ProxyHandler) buildZAIRequest(request *ChatCompletionRequest) *ZAIRequest {
	return &ZAIRequest{
		Stream:    true, // 总是从Z.AI请求流式响应
		Model:     p.settings.UpstreamModel,
		Messages:  request.Messages,
		ChatID:    common.GenerateUUID(),
		ID:        common.GenerateUUID(),
		ModelItem: ZAIModelItem{
			ID:      p.settings.UpstreamModel,
			Name:    "GLM-4.5",
			OwnedBy: "openai",
		},
		BackgroundTasks: map[string]bool{
			"title_generation": true,
			"tags_generation":  true,
		},
		Features: map[string]bool{
			"image_generation": false,
			"code_interpreter": false,
			"web_search":       false,
			"auto_web_search":  false,
		},
		MCPServers:  []string{"deep-web-search"},
		Params:      make(map[string]interface{}),
		ToolServers: []string{},
		Variables: map[string]string{
			"{{USER_NAME}}":        "User",
			"{{USER_LOCATION}}":    "Unknown",
			"{{CURRENT_DATETIME}}": time.Now().Format("2006-01-02 15:04:05"),
		},
	}
}

// makeRequest 发送HTTP请求
func (p *ProxyHandler) makeRequest(headers map[string]string, data *ZAIRequest) (*http.Response, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", p.settings.UpstreamURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 5 * time.Minute, // 5分钟超时
	}

	return client.Do(req)
}

// processStreamingData 处理流式数据
func (p *ProxyHandler) processStreamingData(body io.Reader, model string) ([]string, error) {
	var chunks []string
	completionID := common.GenerateCompletionID()
	scanner := bufio.NewScanner(body)

	// 设置超时
	timeout := time.NewTimer(time.Duration(p.settings.StreamTimeout) * time.Second)
	defer timeout.Stop()

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		if !strings.HasPrefix(line, "data: ") {
			continue
		}

		payload := strings.TrimSpace(line[6:])

		if payload == "[DONE]" {
			// 发送最终chunk
			finalChunk := &ChatCompletionChunk{
				ID:      completionID,
				Object:  "chat.completion.chunk",
				Created: time.Now().Unix(),
				Model:   model,
				Choices: []ChunkChoice{{
					Index:        0,
					Delta:        Delta{},
					FinishReason: stringPtr("stop"),
				}},
			}

			chunkData, _ := json.Marshal(finalChunk)
			chunks = append(chunks, "data: "+string(chunkData)+"\n\n")
			chunks = append(chunks, "data: [DONE]\n\n")
			break
		}

		// 打印原始响应数据
		p.logger.Info("=== ZAI Upstream Response Chunk ===")
		p.logger.Info("Raw response chunk: " + payload)

		// 解析JSON数据
		var zaiResp ZAIResponse
		if err := json.Unmarshal([]byte(payload), &zaiResp); err != nil {
			p.logger.Debug("JSON decode error (skipping): " + err.Error())
			continue
		}

		deltaContent := zaiResp.Data.DeltaContent
		phase := zaiResp.Data.Phase

		// 根据设置决定是否发送thinking内容
		shouldSendContent := true
		if !p.settings.ShowThinkTags && phase == "thinking" {
			shouldSendContent = false
		}

		if deltaContent != "" && shouldSendContent {
			// 转换内容
			transformedDelta := deltaContent
			if p.settings.ShowThinkTags {
				transformedDelta = strings.ReplaceAll(transformedDelta, "<details", "<think")
				transformedDelta = strings.ReplaceAll(transformedDelta, "</details>", "</think>")
			}

			// 创建OpenAI格式的chunk
			chunk := &ChatCompletionChunk{
				ID:      completionID,
				Object:  "chat.completion.chunk",
				Created: time.Now().Unix(),
				Model:   model,
				Choices: []ChunkChoice{{
					Index:        0,
					Delta:        Delta{Content: transformedDelta},
					FinishReason: nil,
				}},
			}

			chunkData, _ := json.Marshal(chunk)
			chunks = append(chunks, "data: "+string(chunkData)+"\n\n")
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return chunks, nil
}

// collectStreamingData 收集流式数据
func (p *ProxyHandler) collectStreamingData(body io.Reader) ([]ZAIResponse, error) {
	var chunks []ZAIResponse
	scanner := bufio.NewScanner(body)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		if !strings.HasPrefix(line, "data: ") {
			continue
		}

		payload := strings.TrimSpace(line[6:])

		if payload == "[DONE]" {
			break
		}

		// 打印原始响应数据
		p.logger.Info("=== ZAI Upstream Response Chunk (Non-Streaming) ===")
		p.logger.Info("Raw response chunk: " + payload)

		var zaiResp ZAIResponse
		if err := json.Unmarshal([]byte(payload), &zaiResp); err != nil {
			p.logger.Debug("JSON decode error (skipping): " + err.Error())
			continue
		}

		chunks = append(chunks, zaiResp)
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return chunks, nil
}

// processNonStreamingChunks 处理非流式数据块
func (p *ProxyHandler) processNonStreamingChunks(chunks []ZAIResponse, model string) (*ChatCompletionResponse, error) {
	if len(chunks) == 0 {
		return nil, errors.New("no response from upstream")
	}

	p.logger.Info(fmt.Sprintf("Total chunks received: %d", len(chunks)))

	// 根据SHOW_THINK_TAGS设置聚合内容
	var fullContent string
	if p.settings.ShowThinkTags {
		// 包含所有内容
		for _, chunk := range chunks {
			fullContent += chunk.Data.DeltaContent
		}
	} else {
		// 只包含answer阶段的内容
		for _, chunk := range chunks {
			if chunk.Data.Phase == "answer" {
				fullContent += chunk.Data.DeltaContent
			}
		}
	}

	// 应用内容转换
	transformedContent := common.TransformContent(fullContent, p.settings.ShowThinkTags)

	// 创建OpenAI兼容响应
	response := &ChatCompletionResponse{
		ID:      common.GenerateCompletionID(),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   model,
		Choices: []Choice{{
			Index: 0,
			Message: Message{
				Role:    "assistant",
				Content: transformedContent,
			},
			FinishReason: stringPtr("stop"),
		}},
	}

	return response, nil
}

// stringPtr 返回字符串指针
func stringPtr(s string) *string {
	return &s
}
